export type WellbeingGoalType =
  | 'sleep_duration'
  | 'sleep_quality'
  | 'mood_tracking'
  | 'meditation_frequency'
  | 'meditation_duration'
  | 'stress_reduction'
  | 'wellbeing_score';

export type ValueType = 'number' | 'rating' | 'frequency' | 'duration' | 'percentage';

export interface GoalRule {
  type: WellbeingGoalType;
  title: string;
  description: string;
  icon: string;
  valueType: ValueType;
  unit: string;
  defaultTarget: number;
  minValue: number;
  maxValue: number;
  step: number;
  placeholder: string;
  helpText: string;
  trackingFrequency: 'daily' | 'weekly' | 'monthly';
  category: 'sleep' | 'mood' | 'meditation' | 'stress';
  benefits: string[];
  tips: string[];
}

export const wellbeingGoalRules: GoalRule[] = [
  {
    type: 'sleep_duration',
    title: 'Sleep Duration',
    description: 'Track your nightly sleep hours for better rest and recovery',
    icon: '😴',
    valueType: 'duration',
    unit: 'hours',
    defaultTarget: 8,
    minValue: 4,
    maxValue: 12,
    step: 0.5,
    placeholder: '8.0',
    helpText: 'Most adults need 7-9 hours of sleep per night',
    trackingFrequency: 'daily',
    category: 'sleep',
    benefits: [
      'Better cognitive function',
      'Improved immune system',
      'Enhanced mood stability',
      'Better physical recovery'
    ],
    tips: [
      'Keep a consistent sleep schedule',
      'Create a relaxing bedtime routine',
      'Avoid screens 1 hour before bed'
    ]
  },
  {
    type: 'sleep_quality',
    title: 'Sleep Quality',
    description: 'Rate your sleep quality to identify patterns and improvements',
    icon: '⭐',
    valueType: 'rating',
    unit: 'out of 10',
    defaultTarget: 8,
    minValue: 1,
    maxValue: 10,
    step: 1,
    placeholder: '8',
    helpText: 'Rate your sleep quality from 1 (very poor) to 10 (excellent)',
    trackingFrequency: 'daily',
    category: 'sleep',
    benefits: [
      'Better sleep awareness',
      'Identify sleep patterns',
      'Improved sleep hygiene',
      'Enhanced well-being'
    ],
    tips: [
      'Keep your bedroom cool and dark',
      'Use comfortable bedding',
      'Track factors affecting your sleep'
    ]
  },
  {
    type: 'mood_tracking',
    title: 'Mood Tracking',
    description: 'Monitor your daily mood to understand emotional patterns',
    icon: '😊',
    valueType: 'rating',
    unit: 'out of 10',
    defaultTarget: 7,
    minValue: 1,
    maxValue: 10,
    step: 1,
    placeholder: '7',
    helpText: 'Rate your overall mood from 1 (very low) to 10 (excellent)',
    trackingFrequency: 'daily',
    category: 'mood',
    benefits: [
      'Emotional awareness',
      'Pattern recognition',
      'Better mental health',
      'Stress identification'
    ],
    tips: [
      'Practice gratitude daily',
      'Engage in activities you enjoy',
      'Connect with supportive people'
    ]
  },
  {
    type: 'meditation_frequency',
    title: 'Meditation Frequency',
    description: 'Track how often you meditate to build a consistent practice',
    icon: '🧘',
    valueType: 'frequency',
    unit: 'times per week',
    defaultTarget: 5,
    minValue: 1,
    maxValue: 14,
    step: 1,
    placeholder: '5',
    helpText: 'Set how many times per week you want to meditate',
    trackingFrequency: 'weekly',
    category: 'meditation',
    benefits: [
      'Reduced stress and anxiety',
      'Improved focus',
      'Better emotional regulation',
      'Enhanced self-awareness'
    ],
    tips: [
      'Start with just 5 minutes daily',
      'Use guided meditation apps',
      'Find a quiet, comfortable space'
    ]
  },
  {
    type: 'meditation_duration',
    title: 'Meditation Duration',
    description: 'Track the length of your meditation sessions',
    icon: '⏰',
    valueType: 'duration',
    unit: 'minutes',
    defaultTarget: 15,
    minValue: 5,
    maxValue: 120,
    step: 5,
    placeholder: '15',
    helpText: 'Set your target meditation session length in minutes',
    trackingFrequency: 'daily',
    category: 'meditation',
    benefits: [
      'Deeper mindfulness practice',
      'Improved concentration',
      'Better stress management',
      'Enhanced mental clarity'
    ],
    tips: [
      'Gradually increase duration over time',
      'Focus on consistency over length',
      'Use a timer to track sessions'
    ]
  },
  {
    type: 'stress_reduction',
    title: 'Stress Level',
    description: 'Monitor and work to reduce your daily stress levels',
    icon: '😌',
    valueType: 'rating',
    unit: 'out of 10',
    defaultTarget: 3,
    minValue: 1,
    maxValue: 10,
    step: 1,
    placeholder: '3',
    helpText: 'Rate your stress level from 1 (very low) to 10 (very high). Lower is better.',
    trackingFrequency: 'daily',
    category: 'stress',
    benefits: [
      'Better stress awareness',
      'Improved coping strategies',
      'Enhanced mental health',
      'Better physical health'
    ],
    tips: [
      'Practice deep breathing exercises',
      'Take regular breaks during work',
      'Engage in physical activity'
    ]
  },
  {
    type: 'wellbeing_score',
    title: 'Overall Wellbeing',
    description: 'Track your overall sense of wellbeing and life satisfaction',
    icon: '📊',
    valueType: 'rating',
    unit: 'out of 10',
    defaultTarget: 8,
    minValue: 1,
    maxValue: 10,
    step: 1,
    placeholder: '8',
    helpText: 'Rate your overall wellbeing from 1 (very poor) to 10 (excellent)',
    trackingFrequency: 'daily',
    category: 'mood',
    benefits: [
      'Holistic health awareness',
      'Life satisfaction tracking',
      'Personal growth insights',
      'Better life balance'
    ],
    tips: [
      'Reflect on all aspects of your life',
      'Celebrate small wins',
      'Focus on what you can control'
    ]
  }
];

// Helper function to get goal rule by type
export const getGoalRule = (type: WellbeingGoalType): GoalRule | undefined => {
  return wellbeingGoalRules.find(rule => rule.type === type);
};

// Helper function to get goals by category
export const getGoalsByCategory = (category: 'sleep' | 'mood' | 'meditation' | 'stress'): GoalRule[] => {
  return wellbeingGoalRules.filter(rule => rule.category === category);
};

// Helper function to format value with unit
export const formatGoalValue = (value: number, rule: GoalRule): string => {
  if (rule.valueType === 'duration' && rule.unit === 'hours') {
    return `${value} ${value === 1 ? 'hour' : 'hours'}`;
  }
  if (rule.valueType === 'duration' && rule.unit === 'minutes') {
    return `${value} ${value === 1 ? 'minute' : 'minutes'}`;
  }
  if (rule.valueType === 'frequency') {
    return `${value} ${rule.unit}`;
  }
  if (rule.valueType === 'rating') {
    return `${value} ${rule.unit}`;
  }
  return `${value} ${rule.unit}`;
};

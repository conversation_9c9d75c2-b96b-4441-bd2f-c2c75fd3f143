import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from "react-native";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { Target, Calendar, Hash, FileText, Save } from "lucide-react-native";
import { Header } from "@/components/ui/Header";
import { useRouter } from "expo-router";
import { Stack } from "expo-router";

const GOAL_TYPES = [
  { value: "sleep_duration", label: "Sleep Duration", icon: "😴" },
  { value: "sleep_quality", label: "Sleep Quality", icon: "⭐" },
  { value: "mood_tracking", label: "Mood Tracking", icon: "😊" },
  { value: "meditation_frequency", label: "Meditation Frequency", icon: "🧘" },
  { value: "meditation_duration", label: "Meditation Duration", icon: "⏰" },
  { value: "stress_reduction", label: "Stress Reduction", icon: "😌" },
  { value: "wellbeing_score", label: "Wellbeing Score", icon: "📊" },
];

export default function CreateWellbeingGoalScreen() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    type: "sleep_duration",
    title: "",
    description: "",
    targetValue: "",
    unit: "",
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
  });

  const createGoal = useMutation(api.wellbeingGoals.createWellbeingGoal);

  const handleSave = async () => {
    if (
      !formData.title.trim() ||
      !formData.targetValue ||
      !formData.unit.trim()
    ) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    try {
      await createGoal({
        type: formData.type as any,
        title: formData.title,
        description: formData.description,
        targetValue: parseFloat(formData.targetValue),
        unit: formData.unit,
        startDate: formData.startDate,
        endDate: formData.endDate,
      });

      router.back();
    } catch (error) {
      Alert.alert("Error", "Failed to save goal");
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      <Stack.Screen options={{ headerShown: false }} />
      <Header title="Create New Goal" showBackButton={true} />
      <ScrollView className="flex-1 p-6">
        {/* Goal Type Selection */}
        <View className="mb-8">
          <View className="mb-4">
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              Choose Your Goal Type
            </Text>
            <Text className="text-sm text-gray-600">
              Select the type of wellbeing goal you want to track
            </Text>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row space-x-3 px-1">
              {GOAL_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  onPress={() =>
                    setFormData((prev) => ({ ...prev, type: type.value }))
                  }
                  className={`px-4 py-4 rounded-2xl border-2 min-w-[120px] ${
                    formData.type === type.value
                      ? "bg-gray-900 border-gray-900 shadow-lg"
                      : "bg-white border-gray-100 shadow-sm"
                  }`}
                >
                  <Text className="text-2xl mb-2 text-center">{type.icon}</Text>
                  <Text
                    className={`text-xs font-medium text-center leading-4 ${
                      formData.type === type.value
                        ? "text-white"
                        : "text-gray-700"
                    }`}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Goal Details Form */}
        <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
          <View className="mb-6">
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              Goal Details
            </Text>
            <Text className="text-sm text-gray-600">
              Provide specific information about your wellbeing goal
            </Text>
          </View>

          {/* Title */}
          <View className="mb-5">
            <View className="flex-row items-center mb-2">
              <Target size={16} color="#6B7280" />
              <Text className="text-sm font-medium text-gray-700 ml-2">
                Goal Title *
              </Text>
            </View>
            <TextInput
              value={formData.title}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, title: text }))
              }
              placeholder="e.g., Sleep 8 hours daily"
              className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400"
            />
          </View>

          {/* Description */}
          <View className="mb-5">
            <View className="flex-row items-center mb-2">
              <FileText size={16} color="#6B7280" />
              <Text className="text-sm font-medium text-gray-700 ml-2">
                Description
              </Text>
            </View>
            <TextInput
              value={formData.description}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, description: text }))
              }
              placeholder="Describe what you want to achieve and why it matters to you"
              multiline
              numberOfLines={3}
              textAlignVertical="top"
              className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400 min-h-[80px]"
            />
          </View>

          {/* Target Value and Unit */}
          <View className="flex-row space-x-4 mb-5">
            <View className="flex-1">
              <View className="flex-row items-center mb-2">
                <Hash size={16} color="#6B7280" />
                <Text className="text-sm font-medium text-gray-700 ml-2">
                  Target Value *
                </Text>
              </View>
              <TextInput
                value={formData.targetValue}
                onChangeText={(text) =>
                  setFormData((prev) => ({ ...prev, targetValue: text }))
                }
                placeholder="8"
                keyboardType="numeric"
                className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400"
              />
            </View>
            <View className="flex-1">
              <View className="flex-row items-center mb-2">
                <Text className="text-sm font-medium text-gray-700">
                  Unit *
                </Text>
              </View>
              <TextInput
                value={formData.unit}
                onChangeText={(text) =>
                  setFormData((prev) => ({ ...prev, unit: text }))
                }
                placeholder="hours"
                className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400"
              />
            </View>
          </View>

          {/* End Date */}
          <View className="mb-0">
            <View className="flex-row items-center mb-2">
              <Calendar size={16} color="#6B7280" />
              <Text className="text-sm font-medium text-gray-700 ml-2">
                Target End Date
              </Text>
            </View>
            <TextInput
              value={formData.endDate}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, endDate: text }))
              }
              placeholder="YYYY-MM-DD"
              className="border border-gray-200 rounded-xl px-4 py-3.5 text-gray-900 bg-gray-50 focus:bg-white focus:border-gray-400"
            />
          </View>
        </View>
      </ScrollView>

      {/* Footer */}
      <View className="p-6 border-t border-gray-100 bg-white">
        <TouchableOpacity
          onPress={handleSave}
          className="flex-row items-center justify-center py-4 px-6 rounded-2xl bg-gray-900 shadow-lg"
        >
          <Save size={20} color="#FFFFFF" />
          <Text className="text-center font-semibold text-white text-base ml-2">
            Create Goal
          </Text>
        </TouchableOpacity>
        <Text className="text-xs text-gray-500 text-center mt-3">
          Track your progress and build healthy habits
        </Text>
      </View>
    </View>
  );
}

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from "react-native";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { X, Target, Calendar, Hash, FileText, Save } from "lucide-react-native";
import { Button } from "@/components/ui/button";
import { Text as UIText } from "@/components/ui/text";
import { Card } from "@/components/ui/card";
import { useRouter } from "expo-router";
import { Stack } from "expo-router";

const GOAL_TYPES = [
  { value: "sleep_duration", label: "Sleep Duration", icon: "😴" },
  { value: "sleep_quality", label: "Sleep Quality", icon: "⭐" },
  { value: "mood_tracking", label: "Mood Tracking", icon: "😊" },
  { value: "meditation_frequency", label: "Meditation Frequency", icon: "🧘" },
  { value: "meditation_duration", label: "Meditation Duration", icon: "⏰" },
  { value: "stress_reduction", label: "Stress Reduction", icon: "😌" },
  { value: "wellbeing_score", label: "Wellbeing Score", icon: "📊" },
];

export default function CreateWellbeingGoalScreen() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    type: "sleep_duration",
    title: "",
    description: "",
    targetValue: "",
    unit: "",
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
  });

  const createGoal = useMutation(api.wellbeingGoals.createWellbeingGoal);

  const handleSave = async () => {
    if (
      !formData.title.trim() ||
      !formData.targetValue ||
      !formData.unit.trim()
    ) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    try {
      await createGoal({
        type: formData.type as any,
        title: formData.title,
        description: formData.description,
        targetValue: parseFloat(formData.targetValue),
        unit: formData.unit,
        startDate: formData.startDate,
        endDate: formData.endDate,
      });

      router.back();
    } catch (error) {
      Alert.alert("Error", "Failed to save goal");
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      <Stack.Screen options={{ title: "Create New Goal", headerBackTitle: "Back" }} />
      <ScrollView className="flex-1 p-6">
        {/* Goal Type Selection */}
        <View className="mb-6">
          <Text className="text-sm font-medium text-gray-700 mb-3">
            Goal Type
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row space-x-3">
              {GOAL_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  onPress={() =>
                    setFormData((prev) => ({ ...prev, type: type.value }))
                  }
                  className={`px-3 py-3 rounded-xl border ${
                    formData.type === type.value
                      ? "bg-gray-900 border-gray-900"
                      : "bg-white border-gray-100"
                  }`}
                >
                  <Text className="text-base mb-1 text-center">
                    {type.icon}
                  </Text>
                  <Text
                    className={`text-xs font-medium text-center ${
                      formData.type === type.value
                        ? "text-white"
                        : "text-gray-600"
                    }`}
                  >
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Title */}
        <View className="mb-4">
          <Text className="text-sm font-medium text-gray-700 mb-2">
            Title *
          </Text>
          <TextInput
            value={formData.title}
            onChangeText={(text) =>
              setFormData((prev) => ({ ...prev, title: text }))
            }
            placeholder="Enter goal title"
            className="border border-gray-100 rounded-xl px-4 py-3 text-gray-900 bg-white"
          />
        </View>

        {/* Description */}
        <View className="mb-4">
          <Text className="text-sm font-medium text-gray-700 mb-2">
            Description
          </Text>
          <TextInput
            value={formData.description}
            onChangeText={(text) =>
              setFormData((prev) => ({ ...prev, description: text }))
            }
            placeholder="Describe your goal"
            multiline
            numberOfLines={3}
            className="border border-gray-100 rounded-xl px-4 py-3 text-gray-900 bg-white"
          />
        </View>

        {/* Target Value and Unit */}
        <View className="flex-row space-x-3 mb-4">
          <View className="flex-1">
            <Text className="text-sm font-medium text-gray-700 mb-2">
              Target Value *
            </Text>
            <TextInput
              value={formData.targetValue}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, targetValue: text }))
              }
              placeholder="0"
              keyboardType="numeric"
              className="border border-gray-100 rounded-xl px-4 py-3 text-gray-900 bg-white"
            />
          </View>
          <View className="flex-1">
            <Text className="text-sm font-medium text-gray-700 mb-2">
              Unit *
            </Text>
            <TextInput
              value={formData.unit}
              onChangeText={(text) =>
                setFormData((prev) => ({ ...prev, unit: text }))
              }
              placeholder="hours, days, etc."
              className="border border-gray-100 rounded-xl px-4 py-3 text-gray-900 bg-white"
            />
          </View>
        </View>

        {/* End Date */}
        <View className="mb-6">
          <Text className="text-sm font-medium text-gray-700 mb-2">
            End Date
          </Text>
          <TextInput
            value={formData.endDate}
            onChangeText={(text) =>
              setFormData((prev) => ({ ...prev, endDate: text }))
            }
            placeholder="YYYY-MM-DD"
            className="border border-gray-100 rounded-xl px-4 py-3 text-gray-900 bg-white"
          />
        </View>
      </ScrollView>

      {/* Footer */}
      <View className="p-6 border-t border-gray-100 bg-white">
        <TouchableOpacity
          onPress={handleSave}
          className="flex-1 py-3 px-4 rounded-xl bg-gray-900"
        >
          <Text className="text-center font-medium text-white">
            Create Goal
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

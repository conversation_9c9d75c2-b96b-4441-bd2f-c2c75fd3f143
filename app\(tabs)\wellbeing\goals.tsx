import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import {
  ChevronLeft,
  Target,
  TrendingUp,
  Calendar,
  Award,
  Plus,
  Edit3,
  Trash2,
  CheckCircle,
  Circle,
  Pause,
  Play,
} from "lucide-react-native";
import { GoalCard } from "@/components/goals/GoalCard";

export default function WellbeingGoals() {
  const router = useRouter();
  const [refreshKey, setRefreshKey] = useState(0);

  const activeGoals = useQuery(api.wellbeingGoals.getActiveWellbeingGoals);
  const allGoals = useQuery(api.wellbeingGoals.getWellbeingGoals, {});

  const handleRefresh = () => {
    setRefreshKey((prev) => prev + 1);
  };

  const handleEditGoal = (goal: any) => {
    router.push("/(tabs)/wellbeing/create-goal");
  };

  const handleCreateGoal = () => {
    router.push("/(tabs)/wellbeing/create-goal");
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="flex-row items-center justify-between px-6 py-4 bg-white border-b border-gray-200">
        <View className="flex-row items-center">
          <TouchableOpacity onPress={() => router.back()} className="mr-4">
            <ChevronLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-semibold text-gray-900">
            Wellbeing Goals
          </Text>
        </View>
        <TouchableOpacity
          onPress={handleCreateGoal}
          className="bg-gray-900 rounded-xl px-4 py-2 flex-row items-center"
        >
          <Plus size={18} color="#ffffff" />
          <Text className="text-white font-medium ml-2">New Goal</Text>
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 px-6 py-6">
        {/* Summary Cards */}
        <View className="flex-row mb-6">
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mr-2">
            <View className="flex-row items-center mb-2">
              <Target size={20} color="#3b82f6" />
              <Text className="text-sm text-gray-600 ml-2">Active Goals</Text>
            </View>
            <Text className="text-2xl font-bold text-gray-900">
              {activeGoals?.length || 0}
            </Text>
          </View>

          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 ml-2">
            <View className="flex-row items-center mb-2">
              <Award size={20} color="#22c55e" />
              <Text className="text-sm text-gray-600 ml-2">Completed</Text>
            </View>
            <Text className="text-2xl font-bold text-gray-900">
              {allGoals?.filter((g) => g.achieved).length || 0}
            </Text>
          </View>
        </View>

        {/* Active Goals */}
        {activeGoals && activeGoals.length > 0 ? (
          <>
            <Text className="text-xl font-semibold text-gray-900 mb-4">
              Active Goals
            </Text>
            {activeGoals.map((goal) => (
              <GoalCard
                key={goal._id}
                goal={goal}
                onEdit={handleEditGoal}
                onRefresh={handleRefresh}
              />
            ))}
          </>
        ) : (
          <View className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 items-center">
            <Target size={48} color="#9ca3af" />
            <Text className="text-lg font-semibold text-gray-900 mt-4 mb-2">
              No Active Goals
            </Text>
            <Text className="text-gray-600 text-center mb-4">
              Set your first wellbeing goal to start tracking your progress
            </Text>
            <TouchableOpacity
              onPress={handleCreateGoal}
              className="bg-gray-900 rounded-xl px-6 py-3"
            >
              <Text className="text-white font-semibold">Create Goal</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Completed Goals */}
        {allGoals && allGoals.filter((g) => g.achieved).length > 0 && (
          <>
            <Text className="text-xl font-semibold text-gray-900 mb-4 mt-6">
              Completed Goals
            </Text>
            {allGoals
              .filter((g) => g.achieved)
              .map((goal) => (
                <GoalCard
                  key={goal._id}
                  goal={goal}
                  onEdit={handleEditGoal}
                  onRefresh={handleRefresh}
                />
              ))}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

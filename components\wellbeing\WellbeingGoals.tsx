import React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Target, Plus, Edit3 } from "lucide-react-native";
import { useRouter } from "expo-router";

export function WellbeingGoals() {
  const router = useRouter();
  const activeGoals = useQuery(api.wellbeingGoals.getActiveWellbeingGoals);
  const currentGoal = activeGoals?.[0]; // Get the primary active goal

  const handleEditGoal = (goal: any) => {
    // For now, we'll navigate to the create screen, but you might want a dedicated edit screen
    router.push("/(tabs)/wellbeing/create-goal");
  };

  const handleCreateGoal = () => {
    router.push("/(tabs)/wellbeing/create-goal");
  };

  return (
    <View className="mx-6 mb-6">
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-xl font-semibold text-gray-900">
          Current Goal
        </Text>
        <TouchableOpacity
          onPress={handleCreateGoal}
          className="flex-row items-center bg-gray-900 rounded-xl px-3 py-2"
        >
          <Plus size={16} color="#ffffff" />
          <Text className="text-white font-medium ml-1">New</Text>
        </TouchableOpacity>
      </View>

      {currentGoal ? (
        <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <View className="w-10 h-10 rounded-xl bg-blue-100 items-center justify-center mr-3">
                <Target size={20} color="#3b82f6" />
              </View>
              <View className="flex-1">
                <Text className="font-semibold text-gray-900 text-base">
                  {currentGoal.title}
                </Text>
                <Text className="text-gray-600 text-sm">
                  {currentGoal.description}
                </Text>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => handleEditGoal(currentGoal)}
              className="p-2"
            >
              <Edit3 size={16} color="#6b7280" />
            </TouchableOpacity>
          </View>

          <View className="mb-2">
            <View className="flex-row items-center justify-between mb-1">
              <Text className="text-sm text-gray-600">Progress</Text>
              <Text className="text-sm font-medium text-gray-900">
                {Math.round(
                  ((currentGoal.currentValue || 0) / currentGoal.targetValue) *
                    100
                )}
                %
              </Text>
            </View>
            <View className="bg-gray-200 rounded-full h-2">
              <View
                className="bg-blue-500 h-2 rounded-full"
                style={{
                  width: `${Math.min(100, ((currentGoal.currentValue || 0) / currentGoal.targetValue) * 100)}%`,
                }}
              />
            </View>
          </View>

          <Text className="text-xs text-gray-600">
            {currentGoal.currentValue || 0} / {currentGoal.targetValue}{" "}
            {currentGoal.unit}
          </Text>
        </View>
      ) : (
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 items-center">
          <View className="w-12 h-12 rounded-xl bg-gray-100 items-center justify-center mb-3">
            <Target size={24} color="#6b7280" />
          </View>
          <Text className="font-semibold text-gray-900 text-base mb-2">
            No Active Goal
          </Text>
          <Text className="text-gray-600 text-sm text-center mb-4">
            Set a wellbeing goal to track your progress and stay motivated
          </Text>
          <TouchableOpacity
            onPress={handleCreateGoal}
            className="bg-gray-900 rounded-xl px-4 py-2 flex-row items-center"
          >
            <Plus size={16} color="#ffffff" />
            <Text className="text-white font-medium ml-2">Create Goal</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

import React, { useState } from "react";
import { Sc<PERSON>View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Header } from "@/components/ui/Header";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Text } from "@/components/ui/text";
import { <PERSON>, Heart, Brain } from "lucide-react-native";
import {
  WellbeingGoals,
  WellbeingMetrics,
  SleepTab,
  MoodTab,
  MeditationTab,
} from "@/components/wellbeing";

export default function WellbeingDashboard() {
  const [tabValue, setTabValue] = useState("sleep");

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        <Header title="Wellbeing" />

        {/* Goals Section */}
        <WellbeingGoals />

        {/* Today's Overview */}
        <WellbeingMetrics />

        {/* Tab Navigation */}
        <Tabs
          value={tabValue}
          onValueChange={setTabValue}
          className="mx-6 mt-4"
        >
          <TabsList
            className="flex-row flex-1"
            style={{ backgroundColor: "#f1f1f1" }}
          >
            <TabsTrigger value="sleep" className="flex-1 flex-row gap-2">
              <Moon size={16} />
              <Text>Sleep</Text>
            </TabsTrigger>
            <TabsTrigger value="mood" className="flex-1 flex-row gap-2">
              <Heart size={16} />
              <Text>Mood</Text>
            </TabsTrigger>
            <TabsTrigger value="meditation" className="flex-1 flex-row gap-2">
              <Brain size={16} />
              <Text>Meditation</Text>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="sleep">
            <SleepTab />
          </TabsContent>
          <TabsContent value="mood">
            <MoodTab />
          </TabsContent>
          <TabsContent value="meditation">
            <MeditationTab />
          </TabsContent>
        </Tabs>
      </ScrollView>
    </SafeAreaView>
  );
}
